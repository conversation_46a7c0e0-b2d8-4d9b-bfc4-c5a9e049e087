#!/usr/bin/env python3
"""
队列功能测试脚本

用于测试Celery队列和Redis连接是否正常工作。
"""

import sys
import time
import requests
import json
from datetime import datetime

# 测试配置
API_BASE_URL = "http://localhost:5001/api/v1"
TEST_CONTENT = """
昨天下午，苹果公司在加利福尼亚州库比蒂诺的总部举行了新产品发布会。
CEO蒂姆·库克宣布推出全新的iPhone 15系列手机，该产品将于下个月正式上市销售。
发布会上，苹果还展示了新款MacBook Pro笔记本电脑的强大性能。
与此同时，特斯拉公司的股价在纳斯达克交易所上涨了5%，达到每股250美元。
"""


def test_redis_health():
    """测试Redis连接健康状态"""
    print("=" * 50)
    print("测试Redis连接健康状态...")
    
    try:
        response = requests.get(f"{API_BASE_URL}/tasks/health/redis")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Redis连接测试成功")
            print(f"状态: {result['status']}")
            if result['data']['status'] == 'success':
                broker = result['data']['broker']
                result_backend = result['data']['result']
                print(f"Broker Redis: {broker['message']}")
                print(f"Result Redis: {result_backend['message']}")
            else:
                print(f"❌ Redis连接异常: {result['data']}")
            return True
        else:
            print(f"❌ Redis健康检查失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Redis健康检查异常: {str(e)}")
        return False


def test_queue_health():
    """测试队列健康状态"""
    print("=" * 50)
    print("测试队列健康状态...")
    
    try:
        response = requests.get(f"{API_BASE_URL}/tasks/health/queue")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 队列健康检查成功")
            print(f"状态: {result['status']}")
            
            data = result['data']
            print(f"Redis状态: {data['redis_status']['status']}")
            print(f"队列信息: {data['queue_info']['status']}")
            print(f"Worker统计: {len(data['worker_stats'])} 个worker")
            print(f"活跃任务: {sum(len(tasks) for tasks in data['active_tasks'].values())} 个")
            return True
        else:
            print(f"❌ 队列健康检查失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 队列健康检查异常: {str(e)}")
        return False


def submit_task():
    """提交测试任务"""
    print("=" * 50)
    print("提交事件提取任务...")
    
    try:
        payload = {
            "task_type": "event_extraction",
            "content": TEST_CONTENT,
            "priority": 5
        }
        
        response = requests.post(
            f"{API_BASE_URL}/tasks/submit",
            json=payload,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            result = response.json()
            task_id = result['data']['task_id']
            print(f"✅ 任务提交成功")
            print(f"任务ID: {task_id}")
            print(f"任务类型: {result['data']['task_type']}")
            print(f"任务状态: {result['data']['status']}")
            print(f"提交时间: {result['data']['submitted_at']}")
            return task_id
        else:
            print(f"❌ 任务提交失败，状态码: {response.status_code}")
            print(f"响应: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 任务提交异常: {str(e)}")
        return None


def check_task_status(task_id):
    """检查任务状态"""
    print("=" * 50)
    print(f"检查任务状态 (ID: {task_id})...")
    
    try:
        response = requests.get(f"{API_BASE_URL}/tasks/status/{task_id}")
        if response.status_code == 200:
            result = response.json()
            data = result['data']
            print(f"✅ 任务状态查询成功")
            print(f"任务ID: {data['task_id']}")
            print(f"任务类型: {data['task_type']}")
            print(f"任务状态: {data['status']}")
            print(f"进度: {data.get('progress', 'N/A')}")
            print(f"提交时间: {data['submitted_at']}")
            print(f"开始时间: {data.get('started_at', 'N/A')}")
            print(f"完成时间: {data.get('completed_at', 'N/A')}")
            print(f"重试次数: {data.get('retry_count', 0)}")
            if data.get('error_message'):
                print(f"错误信息: {data['error_message']}")
            return data['status']
        else:
            print(f"❌ 任务状态查询失败，状态码: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 任务状态查询异常: {str(e)}")
        return None


def get_task_result(task_id):
    """获取任务结果"""
    print("=" * 50)
    print(f"获取任务结果 (ID: {task_id})...")
    
    try:
        response = requests.get(f"{API_BASE_URL}/tasks/result/{task_id}")
        if response.status_code == 200:
            result = response.json()
            data = result['data']
            print(f"✅ 任务结果获取成功")
            print(f"任务ID: {data['task_id']}")
            print(f"任务状态: {data['status']}")
            print(f"处理时间: {data.get('processing_time', 'N/A')} 秒")
            
            if data.get('result'):
                task_result = data['result']
                print(f"提取事件数量: {task_result.get('total', 0)}")
                print("提取的事件:")
                for i, event in enumerate(task_result.get('events', []), 1):
                    print(f"  {i}. 类型: {event['type']}")
                    print(f"     触发词: {event['trigger']}")
                    print(f"     描述: {event['description']}")
                    print(f"     论元数量: {len(event['arguments'])}")
            return True
        elif response.status_code == 202:
            print("⏳ 任务仍在执行中，请稍后查询")
            return False
        else:
            print(f"❌ 任务结果获取失败，状态码: {response.status_code}")
            print(f"响应: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 任务结果获取异常: {str(e)}")
        return False


def main():
    """主测试流程"""
    print("🚀 开始队列功能测试")
    print(f"API地址: {API_BASE_URL}")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 测试Redis连接
    if not test_redis_health():
        print("❌ Redis连接测试失败，请检查Redis服务是否启动")
        return
    
    # 2. 测试队列健康状态
    if not test_queue_health():
        print("❌ 队列健康检查失败，请检查Celery Worker是否启动")
        return
    
    # 3. 提交任务
    task_id = submit_task()
    if not task_id:
        print("❌ 任务提交失败")
        return
    
    # 4. 轮询任务状态直到完成
    max_wait_time = 120  # 最大等待时间（秒）
    check_interval = 3   # 检查间隔（秒）
    waited_time = 0
    
    while waited_time < max_wait_time:
        status = check_task_status(task_id)
        if status in ['SUCCESS', 'FAILURE', 'REVOKED']:
            break
        elif status in ['PENDING', 'STARTED']:
            print(f"⏳ 任务进行中，等待 {check_interval} 秒后再次检查...")
            time.sleep(check_interval)
            waited_time += check_interval
        else:
            print(f"❌ 未知任务状态: {status}")
            break
    
    if waited_time >= max_wait_time:
        print(f"⏰ 任务等待超时 ({max_wait_time} 秒)")
        return
    
    # 5. 获取任务结果
    if status == 'SUCCESS':
        get_task_result(task_id)
    elif status == 'FAILURE':
        print("❌ 任务执行失败")
    
    print("=" * 50)
    print("🎉 队列功能测试完成")


if __name__ == "__main__":
    main()
