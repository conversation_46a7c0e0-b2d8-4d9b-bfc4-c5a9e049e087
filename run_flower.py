#!/usr/bin/env python3
"""
Flower 监控界面启动脚本

用于启动 Celery 的 Web 监控界面 Flower。
"""

import os
import sys
from app.core.config import settings
from app.core.logger_config import get_logger

logger = get_logger()


def main():
    """启动 Flower 监控界面"""
    try:
        logger.info("正在启动 Flower 监控界面...")
        
        # 设置环境变量
        os.environ.setdefault('PYTHONPATH', os.getcwd())
        
        # Flower 默认端口
        flower_port = getattr(settings, 'FLOWER_PORT', 5555)
        flower_host = getattr(settings, 'FLOWER_HOST', '0.0.0.0')
        
        # 构建 Flower 命令
        cmd_args = [
            'celery',
            '-A', 'app.tasks.celery_app',
            'flower',
            f'--port={flower_port}',
            f'--address={flower_host}',
            '--basic_auth=admin:admin123',  # 基础认证，生产环境请修改密码
            '--url_prefix=flower'
        ]
        
        logger.info(f"Flower 配置:")
        logger.info(f"- 应用: app.tasks.celery_app")
        logger.info(f"- 监听地址: {flower_host}:{flower_port}")
        logger.info(f"- 访问地址: http://{flower_host}:{flower_port}")
        logger.info(f"- URL前缀: /flower")
        logger.info(f"- 基础认证: admin:admin123 (请在生产环境中修改)")
        logger.info(f"- Redis Broker: {settings.REDIS_HOST}:{settings.REDIS_PORT}/{settings.REDIS_DB}")
        
        logger.info("Flower 功能:")
        logger.info("- 实时监控 Worker 状态")
        logger.info("- 查看任务执行历史")
        logger.info("- 监控队列长度")
        logger.info("- 查看任务详细信息")
        logger.info("- Worker 性能统计")
        
        # 执行 Flower 命令
        os.execvp('celery', cmd_args)
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在停止 Flower...")
        sys.exit(0)
    except Exception as e:
        logger.error(f"启动 Flower 失败: {str(e)}")
        logger.error("请确保已安装 flower: pip install flower")
        sys.exit(1)


if __name__ == "__main__":
    main()
