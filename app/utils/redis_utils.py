"""
Redis 连接和操作工具模块
"""
import redis
from typing import Optional, Dict, Any
from app.core.config import settings
from app.core.logger_config import get_logger

logger = get_logger()


class RedisConnection:
    """Redis连接管理类"""
    
    def __init__(self):
        self._broker_client: Optional[redis.Redis] = None
        self._result_client: Optional[redis.Redis] = None
    
    @property
    def broker_client(self) -> redis.Redis:
        """获取Broker Redis客户端（用于队列）"""
        if self._broker_client is None:
            self._broker_client = redis.Redis(
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                db=settings.REDIS_DB,
                password=settings.REDIS_PASSWORD,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5
            )
        return self._broker_client
    
    @property
    def result_client(self) -> redis.Redis:
        """获取Result Redis客户端（用于结果存储）"""
        if self._result_client is None:
            self._result_client = redis.Redis(
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                db=settings.REDIS_RESULT_DB,
                password=settings.REDIS_PASSWORD,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5
            )
        return self._result_client
    
    def test_broker_connection(self) -> Dict[str, Any]:
        """测试Broker Redis连接"""
        try:
            client = self.broker_client
            # 执行ping测试
            response = client.ping()
            if response:
                info = client.info()
                return {
                    'status': 'success',
                    'message': 'Broker Redis连接正常',
                    'redis_version': info.get('redis_version'),
                    'used_memory_human': info.get('used_memory_human'),
                    'connected_clients': info.get('connected_clients'),
                    'db_size': client.dbsize()
                }
            else:
                return {
                    'status': 'error',
                    'message': 'Broker Redis ping失败'
                }
        except redis.ConnectionError as e:
            logger.error(f"Broker Redis连接失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'Broker Redis连接失败: {str(e)}'
            }
        except Exception as e:
            logger.error(f"Broker Redis测试异常: {str(e)}")
            return {
                'status': 'error',
                'message': f'Broker Redis测试异常: {str(e)}'
            }
    
    def test_result_connection(self) -> Dict[str, Any]:
        """测试Result Redis连接"""
        try:
            client = self.result_client
            # 执行ping测试
            response = client.ping()
            if response:
                info = client.info()
                return {
                    'status': 'success',
                    'message': 'Result Redis连接正常',
                    'redis_version': info.get('redis_version'),
                    'used_memory_human': info.get('used_memory_human'),
                    'connected_clients': info.get('connected_clients'),
                    'db_size': client.dbsize()
                }
            else:
                return {
                    'status': 'error',
                    'message': 'Result Redis ping失败'
                }
        except redis.ConnectionError as e:
            logger.error(f"Result Redis连接失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'Result Redis连接失败: {str(e)}'
            }
        except Exception as e:
            logger.error(f"Result Redis测试异常: {str(e)}")
            return {
                'status': 'error',
                'message': f'Result Redis测试异常: {str(e)}'
            }
    
    def test_all_connections(self) -> Dict[str, Any]:
        """测试所有Redis连接"""
        broker_result = self.test_broker_connection()
        result_result = self.test_result_connection()
        
        overall_status = 'success' if (
            broker_result['status'] == 'success' and 
            result_result['status'] == 'success'
        ) else 'error'
        
        return {
            'status': overall_status,
            'broker': broker_result,
            'result': result_result
        }
    
    def get_queue_info(self) -> Dict[str, Any]:
        """获取队列信息"""
        try:
            client = self.broker_client
            
            # 获取所有键
            keys = client.keys('celery*')
            
            queue_info = {}
            for key in keys:
                key_type = client.type(key)
                if key_type == 'list':
                    queue_info[key] = {
                        'type': 'queue',
                        'length': client.llen(key)
                    }
                elif key_type == 'string':
                    queue_info[key] = {
                        'type': 'string',
                        'value': client.get(key)
                    }
                elif key_type == 'hash':
                    queue_info[key] = {
                        'type': 'hash',
                        'fields': client.hlen(key)
                    }
            
            return {
                'status': 'success',
                'queues': queue_info,
                'total_keys': len(keys)
            }
            
        except Exception as e:
            logger.error(f"获取队列信息失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'获取队列信息失败: {str(e)}'
            }
    
    def close_connections(self):
        """关闭所有连接"""
        if self._broker_client:
            self._broker_client.close()
            self._broker_client = None
        if self._result_client:
            self._result_client.close()
            self._result_client = None


# 全局Redis连接实例
redis_connection = RedisConnection()


def test_redis_connections() -> Dict[str, Any]:
    """测试Redis连接的便捷函数"""
    return redis_connection.test_all_connections()


def get_redis_queue_info() -> Dict[str, Any]:
    """获取Redis队列信息的便捷函数"""
    return redis_connection.get_queue_info()
