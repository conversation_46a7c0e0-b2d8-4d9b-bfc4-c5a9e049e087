# Celery callback tasks
import requests
from typing import Dict, Any
from app.tasks.celery_app import celery_app
from app.core.config import settings
from app.core.logger_config import get_logger

logger = get_logger()


@celery_app.task(name="tasks.store_result_callback")
def store_result_callback(task_id: str, result: dict) -> None:
    """
    将 LLM 处理结果存储到数据库的回调任务示例。

    Args:
        task_id: 原始 LLM 任务的 ID
        result: LLM 任务的处理结果
    """
    # db = SessionLocal()
    try:
        # print(f"Callback received for task {task_id} with result: {result}")
        # # 示例：将结果存入数据库
        # db_result = LLMResult(
        #     task_id=task_id,
        #     original_input=result.get("original_text"),
        #     processed_output=result.get("processed_text"),
        #     status="COMPLETED"
        # )
        # db.add(db_result)
        # db.commit()
        # print(f"Result for task {task_id} stored in DB.")
        pass  # 占位符，实际应实现数据库写入逻辑
    except Exception as e:
        # print(f"Error in callback for task {task_id}: {e}")
        # db.rollback()
        # 可以在这里记录错误或发送通知
        raise
    finally:
        # db.close()
        pass


@celery_app.task(bind=True, name="tasks.send_task_callback")
def send_task_callback(self, callback_url: str, task_result: Dict[str, Any]) -> None:
    """
    向指定URL发送任务完成回调。

    Args:
        self: Celery 任务实例
        callback_url: 回调URL
        task_result: 任务结果数据
    """
    try:
        logger.info(f"发送任务回调到 {callback_url}，任务ID: {task_result.get('task_id')}")

        response = requests.post(
            callback_url,
            json=task_result,
            timeout=settings.CALLBACK_TIMEOUT,
            headers={'Content-Type': 'application/json'}
        )

        if response.status_code == 200:
            logger.info(f"任务回调发送成功，任务ID: {task_result.get('task_id')}")
        else:
            logger.warning(f"任务回调响应状态码异常: {response.status_code}，任务ID: {task_result.get('task_id')}")

    except requests.exceptions.Timeout:
        logger.error(f"任务回调超时，URL: {callback_url}，任务ID: {task_result.get('task_id')}")
        raise
    except requests.exceptions.RequestException as e:
        logger.error(f"任务回调请求失败: {str(e)}，URL: {callback_url}，任务ID: {task_result.get('task_id')}")
        raise
    except Exception as e:
        logger.error(f"任务回调发送异常: {str(e)}，URL: {callback_url}，任务ID: {task_result.get('task_id')}")
        raise


@celery_app.task(name="tasks.error_handler_task")
def error_handler_task(task_id: str, error_message: str) -> None:
    """
    处理 LLM 任务错误的示例回调任务。

    Args:
        task_id: 发生错误的原始 LLM 任务的 ID
        error_message: 错误信息
    """
    try:
        logger.error(f"任务 {task_id} 执行失败: {error_message}")
        # 这里可以添加错误处理逻辑，如发送通知、记录到数据库等
        pass
    except Exception as e:
        logger.error(f"错误处理任务异常: {str(e)}，任务ID: {task_id}")
        raise
