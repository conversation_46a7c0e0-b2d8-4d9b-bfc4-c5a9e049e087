from datetime import datetime
from typing import Dict, Any
from app.tasks.celery_app import celery_app
from app.services.event_extraction import extract_events
from app.core.logger_config import get_logger

logger = get_logger()


@celery_app.task(bind=True, name="tasks.event_extraction")
def event_extraction_task(self, content: str, callback_url: str = None, echo: str = None) -> Dict[str, Any]:
    """
    事件提取异步任务。

    Args:
        self: Celery 任务实例 (通过 bind=True 注入)
        content: 需要提取事件的文本内容
        callback_url: 可选的回调URL

    Returns:
        Dict[str, Any]: 包含事件提取结果的字典
    """
    try:
        task_id = self.request.id
        logger.info(f"开始执行事件提取任务 {task_id}，文本长度: {len(content)}")

        # 更新任务状态为STARTED
        self.update_state(
            state='STARTED',
            meta={
                'task_id': task_id,
                'task_type': 'event_extraction',
                'started_at': datetime.now().isoformat(),
                'progress': 0
            }
        )

        # 调用事件提取服务
        start_time = datetime.now()
        events_result = extract_events(content)
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()

        # 构建返回结果
        result = {
            'task_id': task_id,
            'task_type': 'event_extraction',
            'status': 'SUCCESS',
            'result': {
                'content': events_result.content,
                'events': [
                    {
                        'type': event.type,
                        'trigger': event.trigger,
                        'description': event.description,
                        'arguments': [
                            {
                                'role': arg.role,
                                'values': arg.values
                            } for arg in event.arguments
                        ]
                    } for event in events_result.events
                ],
                'time': events_result.time.strftime('%Y-%m-%d %H:%M:%S'),
                'total': events_result.total
            },
            'submitted_at': self.request.eta.isoformat() if self.request.eta else start_time.isoformat(),
            'started_at': start_time.isoformat(),
            'completed_at': end_time.isoformat(),
            'processing_time': processing_time
        }

        logger.info(f"事件提取任务 {task_id} 成功完成，提取了 {events_result.total} 个事件，耗时 {processing_time:.2f}秒")

        # 如果有回调URL，替换task_id并触发回调任务
        if callback_url:
            # 替换URL中的{task_id}占位符
            actual_callback_url = callback_url.replace('{task_id}', task_id)
            from app.tasks.callback_tasks import send_task_callback
            send_task_callback.delay(actual_callback_url, result)

        return result

    except Exception as e:
        task_id = self.request.id
        error_message = str(e)
        logger.error(f"事件提取任务 {task_id} 执行失败: {error_message}")

        # 更新任务状态为FAILURE
        self.update_state(
            state='FAILURE',
            meta={
                'task_id': task_id,
                'task_type': 'event_extraction',
                'error_message': error_message,
                'failed_at': datetime.now().isoformat()
            }
        )

        # 如果有回调URL，发送失败通知
        if callback_url:
            # 替换URL中的{task_id}占位符
            actual_callback_url = callback_url.replace('{task_id}', task_id)
            from app.tasks.callback_tasks import send_task_callback
            failure_result = {
                'task_id': task_id,
                'task_type': 'event_extraction',
                'status': 'FAILURE',
                'error_message': error_message,
                'failed_at': datetime.now().isoformat()
            }
            send_task_callback.delay(actual_callback_url, failure_result)

        raise


@celery_app.task(bind=True, name="tasks.process_text_with_llm")
def process_text_with_llm(self, text_input: str) -> dict:
    """
    使用 LLM 处理文本的示例任务。

    Args:
        self: Celery 任务实例 (通过 bind=True 注入)
        text_input: 需要处理的文本字符串

    Returns:
        dict: 包含处理结果的字典
    """
    try:
        # 模拟LLM处理
        result = {"processed_text": f"LLM processed: {text_input}", "original_text": text_input}
        return result
    except Exception as e:
        logger.error(f"文本处理任务失败: {str(e)}")
        raise
