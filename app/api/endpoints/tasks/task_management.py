from datetime import datetime
from typing import Optional
from fastapi import APIRouter, HTTPException, Query
from celery.result import AsyncResult
from celery import states

from app.api.schemas.task_schemas import (
    TaskSubmitRequest, TaskSubmitResponse, TaskStatusResponse, 
    TaskResultResponse, TaskListRequest, TaskListResponse,
    TaskCancelRequest, TaskCancelResponse, TaskStatus, TaskType
)
from app.api.schemas.response_schemas import ApiResponse
from app.tasks.celery_app import celery_app
from app.tasks.llm_tasks import event_extraction_task
from app.core.logger_config import get_logger
from app.utils.redis_utils import test_redis_connections, get_redis_queue_info

logger = get_logger()
router = APIRouter()


@router.post("/submit", response_model=ApiResponse[TaskSubmitResponse])
async def submit_task(request: TaskSubmitRequest):
    """
    提交异步任务。
    
    Args:
        request: 任务提交请求
        
    Returns:
        ApiResponse[TaskSubmitResponse]: 任务提交响应
    """
    try:
        logger.info(f"收到任务提交请求，类型: {request.task_type}, 内容长度: {len(request.content)}")
        
        # 根据任务类型分发到不同的Celery任务
        if request.task_type == TaskType.EVENT_EXTRACTION:
            # 提交事件提取任务
            task = event_extraction_task.apply_async(
                args=[request.content, request.callback_url],
                priority=request.priority
            )
        else:
            raise HTTPException(status_code=400, detail=f"不支持的任务类型: {request.task_type}")
        
        # 构建响应
        response = TaskSubmitResponse(
            task_id=task.id,
            task_type=request.task_type,
            status=TaskStatus.PENDING,
            submitted_at=datetime.now(),
            estimated_completion_time=None  # 可以根据历史数据估算
        )
        
        logger.info(f"任务提交成功，任务ID: {task.id}")
        return ApiResponse(status='success', msg='任务提交成功', data=response)
        
    except Exception as e:
        logger.error(f"任务提交失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"任务提交失败: {str(e)}")


@router.get("/status/{task_id}", response_model=ApiResponse[TaskStatusResponse])
async def get_task_status(task_id: str):
    """
    查询任务状态。
    
    Args:
        task_id: 任务ID
        
    Returns:
        ApiResponse[TaskStatusResponse]: 任务状态响应
    """
    try:
        logger.debug(f"查询任务状态，任务ID: {task_id}")
        
        # 获取任务结果对象
        task_result = AsyncResult(task_id, app=celery_app)
        
        # 获取任务信息
        task_info = task_result.info or {}

        # 获取任务类型，如果没有则抛出异常
        task_type_str = task_info.get('task_type')
        if not task_type_str:
            raise HTTPException(status_code=404, detail="任务信息不完整，无法确定任务类型")

        # 构建状态响应
        response = TaskStatusResponse(
            task_id=task_id,
            task_type=TaskType(task_type_str),
            status=TaskStatus(task_result.state),
            progress=task_info.get('progress'),
            submitted_at=datetime.fromisoformat(task_info.get('submitted_at', datetime.now().isoformat())),
            started_at=datetime.fromisoformat(task_info['started_at']) if task_info.get('started_at') else None,
            completed_at=datetime.fromisoformat(task_info['completed_at']) if task_info.get('completed_at') else None,
            error_message=task_info.get('error_message'),
            retry_count=task_result.retries or 0
        )
        
        return ApiResponse(status='success', msg='查询成功', data=response)
        
    except Exception as e:
        logger.error(f"查询任务状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"查询任务状态失败: {str(e)}")


@router.get("/result/{task_id}", response_model=ApiResponse[TaskResultResponse])
async def get_task_result(task_id: str):
    """
    获取任务结果。
    
    Args:
        task_id: 任务ID
        
    Returns:
        ApiResponse[TaskResultResponse]: 任务结果响应
    """
    try:
        logger.debug(f"获取任务结果，任务ID: {task_id}")
        
        # 获取任务结果对象
        task_result = AsyncResult(task_id, app=celery_app)
        
        # 检查任务状态
        if task_result.state == states.PENDING:
            raise HTTPException(status_code=404, detail="任务不存在或尚未开始")
        elif task_result.state == states.STARTED:
            raise HTTPException(status_code=202, detail="任务正在执行中，请稍后查询")
        elif task_result.state == states.FAILURE:
            error_info = task_result.info or {}
            raise HTTPException(status_code=500, detail=f"任务执行失败: {error_info.get('error_message', '未知错误')}")
        
        # 获取任务结果
        result_data = task_result.result or {}
        
        # 构建结果响应
        response = TaskResultResponse(
            task_id=task_id,
            task_type=TaskType(result_data.get('task_type', 'event_extraction')),
            status=TaskStatus(result_data.get('status', task_result.state)),
            result=result_data.get('result'),
            submitted_at=datetime.fromisoformat(result_data.get('submitted_at', datetime.now().isoformat())),
            started_at=datetime.fromisoformat(result_data['started_at']) if result_data.get('started_at') else None,
            completed_at=datetime.fromisoformat(result_data['completed_at']) if result_data.get('completed_at') else None,
            processing_time=result_data.get('processing_time'),
            error_message=result_data.get('error_message')
        )
        
        return ApiResponse(status='success', msg='获取结果成功', data=response)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务结果失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务结果失败: {str(e)}")


@router.post("/cancel", response_model=ApiResponse[TaskCancelResponse])
async def cancel_task(request: TaskCancelRequest):
    """
    取消任务。
    
    Args:
        request: 任务取消请求
        
    Returns:
        ApiResponse[TaskCancelResponse]: 任务取消响应
    """
    try:
        logger.info(f"取消任务，任务ID: {request.task_id}, 原因: {request.reason}")
        
        # 撤销任务
        celery_app.control.revoke(request.task_id, terminate=True)
        
        # 构建响应
        response = TaskCancelResponse(
            task_id=request.task_id,
            status=TaskStatus.REVOKED,
            cancelled_at=datetime.now(),
            reason=request.reason
        )
        
        logger.info(f"任务取消成功，任务ID: {request.task_id}")
        return ApiResponse(status='success', msg='任务取消成功', data=response)
        
    except Exception as e:
        logger.error(f"取消任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")


@router.get("/list", response_model=ApiResponse[TaskListResponse])
async def list_tasks(
    task_type: Optional[TaskType] = Query(None, description="任务类型过滤"),
    status: Optional[TaskStatus] = Query(None, description="任务状态过滤"),
    limit: int = Query(20, description="返回数量限制", ge=1, le=100),
    offset: int = Query(0, description="偏移量", ge=0)
):
    """
    获取任务列表。
    
    Args:
        task_type: 任务类型过滤
        status: 任务状态过滤
        limit: 返回数量限制
        offset: 偏移量
        
    Returns:
        ApiResponse[TaskListResponse]: 任务列表响应
    """
    try:
        logger.debug(f"获取任务列表，过滤条件 - 类型: {task_type}, 状态: {status}, 限制: {limit}, 偏移: {offset}")
        
        # 注意：这是一个简化的实现
        # 在生产环境中，建议使用数据库存储任务元数据以支持复杂查询
        
        # 获取活跃任务信息
        inspect = celery_app.control.inspect()
        active_tasks = inspect.active() or {}
        scheduled_tasks = inspect.scheduled() or {}
        
        # 这里只返回示例数据，实际应该从Redis或数据库查询
        tasks = []
        
        response = TaskListResponse(
            tasks=tasks,
            total=len(tasks),
            limit=limit,
            offset=offset
        )
        
        return ApiResponse(status='success', msg='获取任务列表成功', data=response)

    except Exception as e:
        logger.error(f"获取任务列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务列表失败: {str(e)}")


@router.get("/health/redis")
async def check_redis_health():
    """
    检查Redis连接健康状态。

    Returns:
        ApiResponse: Redis连接状态
    """
    try:
        logger.debug("检查Redis连接健康状态")

        result = test_redis_connections()

        if result['status'] == 'success':
            return ApiResponse(status='success', msg='Redis连接正常', data=result)
        else:
            return ApiResponse(status='error', msg='Redis连接异常', data=result)

    except Exception as e:
        logger.error(f"Redis健康检查失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Redis健康检查失败: {str(e)}")


@router.get("/health/queue")
async def check_queue_health():
    """
    检查队列健康状态和信息。

    Returns:
        ApiResponse: 队列状态信息
    """
    try:
        logger.debug("检查队列健康状态")

        # 检查Redis连接
        redis_result = test_redis_connections()

        # 获取队列信息（如果Redis可用）
        if redis_result['status'] == 'success':
            queue_info = get_redis_queue_info()
        else:
            queue_info = {
                'status': 'error',
                'message': 'Redis不可用，无法获取队列信息'
            }

        # 获取Celery worker状态
        try:
            inspect = celery_app.control.inspect()
            stats = inspect.stats() or {}
            active = inspect.active() or {}
        except Exception as worker_error:
            logger.warning(f"获取Worker状态失败: {str(worker_error)}")
            stats = {}
            active = {}

        result = {
            'redis_status': redis_result,
            'queue_info': queue_info,
            'worker_stats': stats,
            'active_tasks': active
        }

        overall_status = 'success' if redis_result['status'] == 'success' else 'error'

        return ApiResponse(
            status=overall_status,
            msg='队列健康检查完成',
            data=result
        )

    except Exception as e:
        logger.error(f"队列健康检查失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"队列健康检查失败: {str(e)}")
