from datetime import datetime
from typing import Optional, Any, Dict, List
from pydantic import BaseModel, Field
from enum import Enum


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "PENDING"      # 等待中
    STARTED = "STARTED"      # 已开始
    SUCCESS = "SUCCESS"      # 成功
    FAILURE = "FAILURE"      # 失败
    RETRY = "RETRY"          # 重试中
    REVOKED = "REVOKED"      # 已撤销


class TaskType(str, Enum):
    """任务类型枚举"""
    EVENT_EXTRACTION = "event_extraction"  # 事件提取


class TaskSubmitRequest(BaseModel):
    """任务提交请求模型"""
    task_type: TaskType = Field(..., description="任务类型")
    content: str = Field(..., description="需要处理的文本内容", min_length=1, max_length=50000)
    priority: Optional[int] = Field(default=5, description="任务优先级（1-10，数字越小优先级越高）", ge=1, le=10)
    callback_url: Optional[str] = Field(default=None, description="任务完成后的回调URL（可选）")


class TaskSubmitResponse(BaseModel):
    """任务提交响应模型"""
    task_id: str = Field(..., description="任务ID")
    task_type: TaskType = Field(..., description="任务类型")
    status: TaskStatus = Field(..., description="任务状态")
    submitted_at: datetime = Field(..., description="提交时间")
    estimated_completion_time: Optional[datetime] = Field(default=None, description="预计完成时间")


class TaskStatusResponse(BaseModel):
    """任务状态查询响应模型"""
    task_id: str = Field(..., description="任务ID")
    task_type: TaskType = Field(..., description="任务类型")
    status: TaskStatus = Field(..., description="任务状态")
    progress: Optional[float] = Field(default=None, description="任务进度（0-100）", ge=0, le=100)
    submitted_at: datetime = Field(..., description="提交时间")
    started_at: Optional[datetime] = Field(default=None, description="开始时间")
    completed_at: Optional[datetime] = Field(default=None, description="完成时间")
    error_message: Optional[str] = Field(default=None, description="错误信息（如果失败）")
    retry_count: Optional[int] = Field(default=0, description="重试次数")


class TaskResultResponse(BaseModel):
    """任务结果响应模型"""
    task_id: str = Field(..., description="任务ID")
    task_type: TaskType = Field(..., description="任务类型")
    status: TaskStatus = Field(..., description="任务状态")
    result: Optional[Dict[str, Any]] = Field(default=None, description="任务结果数据")
    submitted_at: datetime = Field(..., description="提交时间")
    started_at: Optional[datetime] = Field(default=None, description="开始时间")
    completed_at: Optional[datetime] = Field(default=None, description="完成时间")
    processing_time: Optional[float] = Field(default=None, description="处理耗时（秒）")
    error_message: Optional[str] = Field(default=None, description="错误信息（如果失败）")


class TaskListRequest(BaseModel):
    """任务列表查询请求模型"""
    task_type: Optional[TaskType] = Field(default=None, description="任务类型过滤")
    status: Optional[TaskStatus] = Field(default=None, description="任务状态过滤")
    limit: Optional[int] = Field(default=20, description="返回数量限制", ge=1, le=100)
    offset: Optional[int] = Field(default=0, description="偏移量", ge=0)


class TaskListResponse(BaseModel):
    """任务列表响应模型"""
    tasks: List[TaskStatusResponse] = Field(..., description="任务列表")
    total: int = Field(..., description="总任务数")
    limit: int = Field(..., description="返回数量限制")
    offset: int = Field(..., description="偏移量")


class TaskCancelRequest(BaseModel):
    """任务取消请求模型"""
    task_id: str = Field(..., description="要取消的任务ID")
    reason: Optional[str] = Field(default=None, description="取消原因")


class TaskCancelResponse(BaseModel):
    """任务取消响应模型"""
    task_id: str = Field(..., description="任务ID")
    status: TaskStatus = Field(..., description="任务状态")
    cancelled_at: datetime = Field(..., description="取消时间")
    reason: Optional[str] = Field(default=None, description="取消原因")
