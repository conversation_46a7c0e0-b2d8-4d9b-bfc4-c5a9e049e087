from fastapi import APIRouter
from app.api.endpoints import health
from app.api.endpoints.event_extraction import event_extraction
from app.api.endpoints.tasks import task_management

api_router = APIRouter()

api_router.include_router(health.router, prefix='/api/v1', tags=["健康检查"])
api_router.include_router(event_extraction.router, prefix='/api/v1/event-extraction', tags=["事件抽取"])
api_router.include_router(task_management.router, prefix='/api/v1/tasks', tags=["任务管理"])
