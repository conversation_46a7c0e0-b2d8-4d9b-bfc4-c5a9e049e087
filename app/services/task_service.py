"""
任务管理服务模块

提供任务提交、查询、管理等功能的高级接口。
"""

from datetime import datetime
from typing import Dict, Any, List, Optional
from celery.result import AsyncResult
from celery import states

from app.tasks.celery_app import celery_app
from app.tasks.llm_tasks import event_extraction_task
from app.api.schemas.task_schemas import TaskType, TaskStatus
from app.core.logger_config import get_logger

logger = get_logger()


class TaskService:
    """任务管理服务类"""
    
    def __init__(self):
        """初始化任务服务"""
        logger.info("TaskService 初始化完成")
    
    def submit_event_extraction_task(
        self, 
        content: str, 
        priority: int = 5, 
        callback_url: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        提交事件提取任务
        
        Args:
            content: 需要提取事件的文本内容
            priority: 任务优先级（1-10，数字越小优先级越高）
            callback_url: 可选的回调URL
            
        Returns:
            Dict[str, Any]: 包含任务ID和基本信息的字典
        """
        try:
            logger.info(f"提交事件提取任务，内容长度: {len(content)}, 优先级: {priority}")
            
            # 提交Celery任务
            task = event_extraction_task.apply_async(
                args=[content, callback_url],
                priority=priority
            )
            
            result = {
                'task_id': task.id,
                'task_type': TaskType.EVENT_EXTRACTION,
                'status': TaskStatus.PENDING,
                'submitted_at': datetime.now(),
                'priority': priority,
                'callback_url': callback_url
            }
            
            logger.info(f"事件提取任务提交成功，任务ID: {task.id}")
            return result
            
        except Exception as e:
            logger.error(f"提交事件提取任务失败: {str(e)}")
            raise
    
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict[str, Any]: 任务状态信息
        """
        try:
            logger.debug(f"获取任务状态，任务ID: {task_id}")
            
            # 获取任务结果对象
            task_result = AsyncResult(task_id, app=celery_app)
            
            # 获取任务信息
            task_info = task_result.info or {}
            
            # 构建状态信息
            status_info = {
                'task_id': task_id,
                'task_type': task_info.get('task_type', TaskType.EVENT_EXTRACTION),
                'status': task_result.state,
                'progress': task_info.get('progress'),
                'submitted_at': task_info.get('submitted_at'),
                'started_at': task_info.get('started_at'),
                'completed_at': task_info.get('completed_at'),
                'error_message': task_info.get('error_message'),
                'retry_count': task_result.retries or 0
            }
            
            return status_info
            
        except Exception as e:
            logger.error(f"获取任务状态失败: {str(e)}")
            raise
    
    def get_task_result(self, task_id: str) -> Dict[str, Any]:
        """
        获取任务结果
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict[str, Any]: 任务结果信息
        """
        try:
            logger.debug(f"获取任务结果，任务ID: {task_id}")
            
            # 获取任务结果对象
            task_result = AsyncResult(task_id, app=celery_app)
            
            # 检查任务状态
            if task_result.state == states.PENDING:
                raise ValueError("任务不存在或尚未开始")
            elif task_result.state == states.STARTED:
                raise ValueError("任务正在执行中，请稍后查询")
            elif task_result.state == states.FAILURE:
                error_info = task_result.info or {}
                raise ValueError(f"任务执行失败: {error_info.get('error_message', '未知错误')}")
            
            # 获取任务结果
            result_data = task_result.result or {}
            
            return result_data
            
        except Exception as e:
            logger.error(f"获取任务结果失败: {str(e)}")
            raise
    
    def cancel_task(self, task_id: str, reason: Optional[str] = None) -> Dict[str, Any]:
        """
        取消任务
        
        Args:
            task_id: 任务ID
            reason: 取消原因
            
        Returns:
            Dict[str, Any]: 取消结果信息
        """
        try:
            logger.info(f"取消任务，任务ID: {task_id}, 原因: {reason}")
            
            # 撤销任务
            celery_app.control.revoke(task_id, terminate=True)
            
            result = {
                'task_id': task_id,
                'status': TaskStatus.REVOKED,
                'cancelled_at': datetime.now(),
                'reason': reason
            }
            
            logger.info(f"任务取消成功，任务ID: {task_id}")
            return result
            
        except Exception as e:
            logger.error(f"取消任务失败: {str(e)}")
            raise
    
    def get_worker_status(self) -> Dict[str, Any]:
        """
        获取Worker状态信息
        
        Returns:
            Dict[str, Any]: Worker状态信息
        """
        try:
            logger.debug("获取Worker状态信息")
            
            inspect = celery_app.control.inspect()
            
            # 获取各种状态信息
            stats = inspect.stats() or {}
            active = inspect.active() or {}
            scheduled = inspect.scheduled() or {}
            reserved = inspect.reserved() or {}
            
            result = {
                'stats': stats,
                'active_tasks': active,
                'scheduled_tasks': scheduled,
                'reserved_tasks': reserved,
                'worker_count': len(stats),
                'total_active_tasks': sum(len(tasks) for tasks in active.values()),
                'total_scheduled_tasks': sum(len(tasks) for tasks in scheduled.values())
            }
            
            return result
            
        except Exception as e:
            logger.error(f"获取Worker状态失败: {str(e)}")
            raise
    
    def get_queue_statistics(self) -> Dict[str, Any]:
        """
        获取队列统计信息
        
        Returns:
            Dict[str, Any]: 队列统计信息
        """
        try:
            logger.debug("获取队列统计信息")
            
            # 这里可以添加更详细的队列统计逻辑
            # 例如从Redis获取队列长度、任务数量等
            
            from app.utils.redis_utils import get_redis_queue_info
            queue_info = get_redis_queue_info()
            
            worker_status = self.get_worker_status()
            
            result = {
                'queue_info': queue_info,
                'worker_status': worker_status,
                'timestamp': datetime.now().isoformat()
            }
            
            return result
            
        except Exception as e:
            logger.error(f"获取队列统计信息失败: {str(e)}")
            raise


# 创建全局服务实例
task_service = TaskService()


# 便捷函数
def submit_event_extraction_task(
    content: str, 
    priority: int = 5, 
    callback_url: Optional[str] = None
) -> Dict[str, Any]:
    """
    便捷函数：提交事件提取任务
    """
    return task_service.submit_event_extraction_task(content, priority, callback_url)


def get_task_status(task_id: str) -> Dict[str, Any]:
    """
    便捷函数：获取任务状态
    """
    return task_service.get_task_status(task_id)


def get_task_result(task_id: str) -> Dict[str, Any]:
    """
    便捷函数：获取任务结果
    """
    return task_service.get_task_result(task_id)


def cancel_task(task_id: str, reason: Optional[str] = None) -> Dict[str, Any]:
    """
    便捷函数：取消任务
    """
    return task_service.cancel_task(task_id, reason)
