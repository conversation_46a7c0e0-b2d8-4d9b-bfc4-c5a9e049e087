#!/usr/bin/env python3
"""
简单的回调服务器

用于接收和显示队列任务的回调数据。
"""

from fastapi import FastAPI, Request
from datetime import datetime
import json
import uvicorn

app = FastAPI(title="Callback Server", description="接收队列任务回调的测试服务器")

# 存储接收到的回调数据
callback_history = []


@app.post("/callback/{task_id}")
async def receive_callback(task_id: str, request: Request):
    """
    接收任务回调
    
    Args:
        task_id: 任务ID
        request: FastAPI请求对象
        
    Returns:
        dict: 确认响应
    """
    try:
        # 获取请求体
        body = await request.body()
        callback_data = json.loads(body.decode('utf-8'))
        
        # 记录回调信息
        callback_info = {
            'received_at': datetime.now().isoformat(),
            'task_id': task_id,
            'url_task_id': task_id,  # URL中的task_id
            'data': callback_data
        }
        
        callback_history.append(callback_info)
        
        # 打印回调信息
        print("=" * 80)
        print(f"🔔 收到任务回调 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"任务ID: {task_id}")
        print(f"任务状态: {callback_data.get('status', 'UNKNOWN')}")
        print(f"任务类型: {callback_data.get('task_type', 'UNKNOWN')}")
        
        if callback_data.get('status') == 'SUCCESS':
            print("✅ 任务执行成功")
            if 'processing_time' in callback_data:
                print(f"处理时间: {callback_data['processing_time']} 秒")
            if 'result' in callback_data and callback_data['result']:
                result = callback_data['result']
                if 'total' in result:
                    print(f"提取事件数量: {result['total']}")
        elif callback_data.get('status') == 'FAILURE':
            print("❌ 任务执行失败")
            if 'error_message' in callback_data:
                print(f"错误信息: {callback_data['error_message']}")
        
        print(f"完整回调数据:")
        print(json.dumps(callback_data, indent=2, ensure_ascii=False))
        print("=" * 80)
        
        return {
            "status": "success",
            "message": f"回调接收成功，任务ID: {task_id}",
            "received_at": callback_info['received_at']
        }
        
    except Exception as e:
        error_msg = f"处理回调时发生错误: {str(e)}"
        print(f"❌ {error_msg}")
        
        # 仍然记录错误的回调
        error_info = {
            'received_at': datetime.now().isoformat(),
            'task_id': task_id,
            'url_task_id': task_id,
            'error': error_msg,
            'raw_body': body.decode('utf-8') if 'body' in locals() else None
        }
        callback_history.append(error_info)
        
        return {
            "status": "error",
            "message": error_msg,
            "received_at": datetime.now().isoformat()
        }


@app.get("/callback/history")
async def get_callback_history():
    """
    获取回调历史记录
    
    Returns:
        dict: 回调历史数据
    """
    return {
        "total_callbacks": len(callback_history),
        "history": callback_history
    }


@app.get("/callback/history/{task_id}")
async def get_task_callback_history(task_id: str):
    """
    获取特定任务的回调历史
    
    Args:
        task_id: 任务ID
        
    Returns:
        dict: 特定任务的回调数据
    """
    task_callbacks = [
        callback for callback in callback_history 
        if callback.get('task_id') == task_id or callback.get('url_task_id') == task_id
    ]
    
    return {
        "task_id": task_id,
        "callback_count": len(task_callbacks),
        "callbacks": task_callbacks
    }


@app.get("/")
async def root():
    """
    根路径，显示服务器状态
    
    Returns:
        dict: 服务器状态信息
    """
    return {
        "service": "Callback Server",
        "status": "running",
        "description": "接收队列任务回调的测试服务器",
        "endpoints": {
            "receive_callback": "POST /callback/{task_id}",
            "callback_history": "GET /callback/history",
            "task_callback_history": "GET /callback/history/{task_id}"
        },
        "total_callbacks_received": len(callback_history),
        "server_time": datetime.now().isoformat()
    }


@app.get("/health")
async def health_check():
    """
    健康检查端点
    
    Returns:
        dict: 健康状态
    """
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "callbacks_received": len(callback_history)
    }


def main():
    """启动回调服务器"""
    print("🚀 启动回调服务器")
    print("=" * 50)
    print("服务器信息:")
    print("- 地址: http://127.0.0.1:80")
    print("- 回调端点: POST /callback/{task_id}")
    print("- 历史记录: GET /callback/history")
    print("- 健康检查: GET /health")
    print("=" * 50)
    print("等待接收回调...")
    print()
    
    # 启动服务器
    uvicorn.run(
        app,
        host="127.0.0.1",
        port=80,
        log_level="info"
    )


if __name__ == "__main__":
    main()
