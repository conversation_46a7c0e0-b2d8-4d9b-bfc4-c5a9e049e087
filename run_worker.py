#!/usr/bin/env python3
"""
Celery Worker 启动脚本

用于启动 LLMFlowHub 的 Celery Worker 进程。
"""

import os
import sys
from app.core.config import settings
from app.core.logger_config import get_logger

logger = get_logger()


def main():
    """启动 Celery Worker"""
    try:
        logger.info("正在启动 LLMFlowHub Celery Worker...")
        
        # 设置环境变量
        os.environ.setdefault('PYTHONPATH', os.getcwd())
        
        # 构建 Celery Worker 命令
        # 使用 solo 池以确保 Windows 兼容性
        cmd_args = [
            'celery',
            '-A', 'app.tasks.celery_app',
            'worker',
            '--loglevel=info',
            '--concurrency=1',
            '--pool=solo'
        ]
        
        # 如果在生产环境，可以调整参数
        if settings.ENVIRONMENT == 'production':
            # 生产环境可以使用更多并发
            cmd_args = [
                'celery',
                '-A', 'app.tasks.celery_app',
                'worker',
                '--loglevel=warning',
                '--concurrency=4',
                '--pool=solo'
            ]
        
        logger.info(f"Worker 配置:")
        logger.info(f"- 应用: app.tasks.celery_app")
        logger.info(f"- 日志级别: {'warning' if settings.ENVIRONMENT == 'production' else 'info'}")
        logger.info(f"- 并发数: {'4' if settings.ENVIRONMENT == 'production' else '1'}")
        logger.info(f"- 池类型: solo (Windows 兼容)")
        logger.info(f"- Redis Broker: {settings.REDIS_HOST}:{settings.REDIS_PORT}/{settings.REDIS_DB}")
        logger.info(f"- Redis Result Backend: {settings.REDIS_HOST}:{settings.REDIS_PORT}/{settings.REDIS_RESULT_DB}")
        
        # 执行 Celery Worker 命令
        os.execvp('celery', cmd_args)
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在停止 Worker...")
        sys.exit(0)
    except Exception as e:
        logger.error(f"启动 Worker 失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
