# 回调API测试服务

这是一个简单的回调API测试服务，用于接收和打印回调请求。

## 功能

- 接收回调请求并打印请求头和请求体
- 支持普通回调和带任务ID的回调
- 尝试解析JSON格式的请求体并格式化输出

## 使用方法

### 启动服务

Windows用户可以直接双击 `start_callback_server.bat` 文件启动服务。

或者，您可以在命令行中运行：

```bash
cd tests/callback_api
python app.py
```

服务将在 `http://localhost:8001` 上启动。

### 回调端点

服务提供以下回调端点：

1. **普通回调**：`http://localhost:8001/callback`
2. **带任务ID的回调**：`http://localhost:8001/callback/{task_id}`

### 在队列任务中使用

在创建队列任务时，可以设置回调URL：

```python
task_create = TaskCreate(
    task_type=TaskType.EVENT_EXTRACTION,
    params=task_params,
    callback_url="http://localhost:8001/callback"  # 或 "http://localhost:8001/callback/{task_id}"
)
```

## 输出示例

服务会在控制台打印接收到的回调请求信息，例如：

```
==================================================
收到回调请求
==================================================

请求头:
host: localhost:8001
user-agent: python-requests/2.28.1
accept-encoding: gzip, deflate
accept: */*
connection: keep-alive
content-length: 123
content-type: application/json

请求体:
{"status": "success", "msg": "任务已完成", "data": {"task_id": "123456", "result": "事件提取结果"}}

请求体 (JSON格式):
{
  "status": "success",
  "msg": "任务已完成",
  "data": {
    "task_id": "123456",
    "result": "事件提取结果"
  }
}
```
