"""
回调API测试模块。

该模块提供了一个简单的FastAPI应用，用于接收和打印回调请求。
"""
import json
import uvicorn
from fastapi import FastAPI, Request, Body
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import Dict, Any, Optional

# 创建FastAPI应用
app = FastAPI(title="回调API测试服务")


@app.get("/")
async def root():
    """根路径处理函数"""
    return {"message": "回调API测试服务已启动"}


@app.post("/callback")
async def callback(request: Request):
    """
    接收回调请求并打印内容。

    参数:
        request: 请求对象

    返回:
        成功响应
    """
    # 获取请求头
    headers = dict(request.headers)
    print("\n" + "="*50)
    print("收到回调请求")
    print("="*50)

    # 获取查询参数
    query_params = dict(request.query_params)
    print("\n查询参数:")
    for key, value in query_params.items():
        print(f"{key}: {value}")

    # 打印请求头
    print("\n请求头:")
    for key, value in headers.items():
        print(f"{key}: {value}")

    # 获取并打印请求体
    body = await request.body()
    body_text = body.decode('utf-8')
    print("\n请求体:")
    print(body_text)

    # 尝试解析JSON
    try:
        body_json = json.loads(body_text)
        print("\n请求体 (JSON格式):")
        print(json.dumps(body_json, ensure_ascii=False, indent=2))
    except:
        print("\n请求体不是有效的JSON格式")

    # 返回成功响应，包含查询参数
    import time
    return JSONResponse(
        status_code=200,
        content={
            "status": "success",
            "message": "回调请求已接收",
            "received_at": str(time.time()),
            "query_params": query_params
        }
    )


@app.post("/callback/{task_id}")
async def callback_with_task_id(task_id: str, request: Request):
    """
    接收带任务ID的回调请求并打印内容。

    参数:
        task_id: 任务ID
        request: 请求对象

    返回:
        成功响应
    """
    # 获取请求头
    headers = dict(request.headers)
    print("\n" + "="*50)
    print(f"收到任务ID为 {task_id} 的回调请求")
    print("="*50)

    # 获取查询参数
    query_params = dict(request.query_params)
    print("\n查询参数:")
    for key, value in query_params.items():
        print(f"{key}: {value}")

    # 打印请求头
    print("\n请求头:")
    for key, value in headers.items():
        print(f"{key}: {value}")

    # 获取并打印请求体
    body = await request.body()
    body_text = body.decode('utf-8')
    print("\n请求体:")
    print(body_text)

    # 尝试解析JSON
    try:
        body_json = json.loads(body_text)
        print("\n请求体 (JSON格式):")
        print(json.dumps(body_json, ensure_ascii=False, indent=2))
    except:
        print("\n请求体不是有效的JSON格式")

    # 返回成功响应，包含查询参数
    import time
    return JSONResponse(
        status_code=200,
        content={
            "status": "success",
            "message": f"任务ID为 {task_id} 的回调请求已接收",
            "task_id": task_id,
            "received_at": str(time.time()),
            "query_params": query_params
        }
    )


if __name__ == "__main__":
    # 启动服务器
    print("="*50)
    print("回调API测试服务启动中...")
    print("="*50)
    uvicorn.run(app, host="0.0.0.0", port=5678)
