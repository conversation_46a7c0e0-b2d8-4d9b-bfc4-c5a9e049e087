@echo off
R<PERSON> Script to run Ce<PERSON>y worker for LLMFlowHub

echo ========================================
echo Starting LLMFlowHub Celery Worker
echo ========================================

REM Activate virtual environment if you have one
if exist ".venv\Scripts\activate.bat" (
    echo Activating virtual environment...
    CALL .venv\Scripts\activate.bat
)

REM Set PYTHONPATH to ensure app module is discoverable
SET PYTHONPATH=%CD%

echo Current directory: %CD%
echo PYTHONPATH: %PYTHONPATH%
echo.

echo Starting Celery worker...
echo Worker configuration:
echo - App: app.tasks.celery_app
echo - Log level: info
echo - Concurrency: 1
echo - Pool: solo (Windows compatible)
echo.

REM Start Celery worker with Windows-compatible settings
celery -A app.tasks.celery_app worker --loglevel=info -c 1 -P solo

REM Alternative configurations (uncomment if needed):
REM For higher concurrency with eventlet (install: pip install eventlet)
REM celery -A app.tasks.celery_app worker --loglevel=info -c 4 -P eventlet

REM For higher concurrency with gevent (install: pip install gevent)
REM celery -A app.tasks.celery_app worker --loglevel=info -c 4 -P gevent

echo.
echo Celery worker stopped.
pause