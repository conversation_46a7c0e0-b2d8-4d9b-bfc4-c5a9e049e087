[project]
name = "LLMFlowHub"
version = "0.0.1"
description = "智能信息处理与LLM能力集成的API中枢。"
requires-python = ">=3.10"
dependencies = [
    "uv>=0.7.14",
    "fastapi>=0.115.13",
    "pydantic>=2.11.7",
    "pydantic-settings>=2.10.0",
    "python-dotenv>=1.1.0",
    "uvicorn>=0.34.3",
    "click>=8.2.1",
    "python-multipart>=0.0.20",
    "beautifulsoup4>=4.13.4",
    "json-repair>=0.47.2",
    "celery[redis]>=5.5.3",
    "langchain>=0.3.26",
    "langchain-core==0.3.66",
    "langchain-openai>=0.3.25",
    "sqlalchemy>=2.0.41",
    "sqlalchemy-utils>=0.41.2",
    "pymysql>=1.1.1",
    "pyjwt==2.10.1",
    "tenacity>=9.1.2",
    "celery-stubs==0.1.3",
]

[dependency-groups]
dev = [
    "pytest>=8.4.1",
]
