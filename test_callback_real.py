#!/usr/bin/env python3
"""
真实回调功能测试

测试向外部URL发送回调是否正常工作。
使用httpbin.org作为测试回调接收端点。
"""

import requests
import json
import time
from datetime import datetime

# 测试配置
API_BASE_URL = "http://localhost:5001/api/v1"
# 使用httpbin.org作为测试回调接收端点
CALLBACK_URL = "https://httpbin.org/post"
TEST_CONTENT = """
昨天下午，苹果公司在加利福尼亚州库比蒂诺的总部举行了新产品发布会。
CEO蒂姆·库克宣布推出全新的iPhone 15系列手机，该产品将于下个月正式上市销售。
"""


def test_callback_with_httpbin():
    """使用httpbin.org测试回调功能"""
    print("🚀 开始真实回调功能测试")
    print(f"API地址: {API_BASE_URL}")
    print(f"回调URL: {CALLBACK_URL}")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)

    # 1. 提交带回调URL的任务
    print("1. 提交带回调URL的事件提取任务...")
    try:
        payload = {
            "task_type": "event_extraction",
            "content": TEST_CONTENT,
            "priority": 5,
            "callback_url": CALLBACK_URL
        }
        
        response = requests.post(
            f"{API_BASE_URL}/tasks/submit",
            json=payload,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            result = response.json()
            task_id = result['data']['task_id']
            print(f"✅ 任务提交成功")
            print(f"任务ID: {task_id}")
            print(f"回调URL: {CALLBACK_URL}")
            print(f"任务状态: {result['data']['status']}")
            return task_id
        else:
            print(f"❌ 任务提交失败，状态码: {response.status_code}")
            print(f"响应: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 任务提交异常: {str(e)}")
        return None


def monitor_task_and_callback(task_id):
    """监控任务进度并等待回调"""
    print("=" * 60)
    print(f"2. 监控任务进度并等待回调 (ID: {task_id})...")
    
    max_wait_time = 120  # 最大等待时间（秒）
    check_interval = 5   # 检查间隔（秒）
    waited_time = 0
    
    while waited_time < max_wait_time:
        try:
            response = requests.get(f"{API_BASE_URL}/tasks/status/{task_id}")
            if response.status_code == 200:
                result = response.json()
                data = result['data']
                status = data['status']
                
                print(f"⏱️  [{datetime.now().strftime('%H:%M:%S')}] 任务状态: {status}")
                
                if status in ['SUCCESS', 'FAILURE', 'REVOKED']:
                    print(f"✅ 任务完成，最终状态: {status}")
                    if status == 'SUCCESS':
                        print(f"完成时间: {data.get('completed_at', 'N/A')}")
                    elif status == 'FAILURE':
                        print(f"错误信息: {data.get('error_message', 'N/A')}")
                    
                    # 任务完成后，等待一段时间让回调发送
                    print("⏳ 等待回调发送...")
                    time.sleep(10)
                    return status
                elif status in ['PENDING', 'STARTED']:
                    if status == 'STARTED':
                        print(f"   开始时间: {data.get('started_at', 'N/A')}")
                        print(f"   进度: {data.get('progress', 'N/A')}")
                    time.sleep(check_interval)
                    waited_time += check_interval
                else:
                    print(f"❌ 未知任务状态: {status}")
                    break
            else:
                print(f"❌ 状态查询失败，状态码: {response.status_code}")
                break
        except Exception as e:
            print(f"❌ 状态查询异常: {str(e)}")
            break
    
    if waited_time >= max_wait_time:
        print(f"⏰ 任务监控超时 ({max_wait_time} 秒)")
        return "TIMEOUT"
    
    return "UNKNOWN"


def check_callback_received():
    """检查httpbin.org是否收到了回调"""
    print("=" * 60)
    print("3. 检查回调是否发送成功...")
    
    print("💡 说明:")
    print("- 由于httpbin.org不保存请求历史，我们无法直接验证回调是否到达")
    print("- 但可以通过Celery Worker日志查看回调发送状态")
    print("- 如果回调发送成功，Worker日志会显示HTTP请求成功")
    print("- 如果回调发送失败，Worker日志会显示错误信息")
    print()
    print("🔍 请检查Celery Worker控制台输出，查找类似以下的日志:")
    print("  ✅ 成功: 'HTTP Request: POST https://httpbin.org/post \"HTTP/1.1 200 OK\"'")
    print("  ❌ 失败: 'HTTP Request: POST https://httpbin.org/post \"HTTP/1.1 xxx Error\"'")


def test_no_callback():
    """测试不带回调URL的任务"""
    print("=" * 60)
    print("4. 测试不带回调URL的任务...")
    
    try:
        payload = {
            "task_type": "event_extraction",
            "content": "简短测试文本：苹果公司发布新产品。",
            "priority": 5
            # 不包含callback_url
        }
        
        response = requests.post(
            f"{API_BASE_URL}/tasks/submit",
            json=payload,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            result = response.json()
            task_id = result['data']['task_id']
            print(f"✅ 无回调任务提交成功")
            print(f"任务ID: {task_id}")
            print("📝 此任务不会发送回调，Worker日志中不应出现回调相关信息")
            return task_id
        else:
            print(f"❌ 任务提交失败，状态码: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 任务提交异常: {str(e)}")
        return None


def main():
    """主测试流程"""
    print("📋 回调功能测试说明:")
    print("1. 提交带回调URL的任务")
    print("2. 监控任务执行状态")
    print("3. 检查Worker日志中的回调发送状态")
    print("4. 测试不带回调的任务作为对比")
    print()
    
    # 测试带回调的任务
    task_id = test_callback_with_httpbin()
    if task_id:
        final_status = monitor_task_and_callback(task_id)
        check_callback_received()
    
    # 测试不带回调的任务
    no_callback_task_id = test_no_callback()
    
    print("=" * 60)
    print("🎉 回调功能测试完成")
    print()
    print("📊 测试结果验证:")
    print("1. 检查Celery Worker日志，确认回调请求是否发送")
    print("2. 带回调的任务应该显示HTTP POST请求到httpbin.org")
    print("3. 不带回调的任务不应该有回调相关日志")
    print()
    print("💡 回调机制说明:")
    print("- callback_url为空或None时，不发送回调")
    print("- callback_url有值时，任务完成后自动发送POST回调")
    print("- 回调失败会在Worker日志中记录错误信息")
    print("- 回调成功会在Worker日志中显示HTTP 200响应")


if __name__ == "__main__":
    main()
