@echo off
echo Testing API structure without Redis...
CALL .venv\Scripts\activate.bat
python -c "
import requests
import json

print('=== Testing API Structure ===')
base_url = 'http://localhost:5001/api/v1'

# Test health check
print('\n1. Testing health check...')
try:
    response = requests.get(f'{base_url}/health/')
    print(f'Status: {response.status_code}')
    if response.status_code == 200:
        print('✅ Health check passed')
    else:
        print('❌ Health check failed')
except Exception as e:
    print(f'❌ Health check error: {e}')

# Test event extraction (sync)
print('\n2. Testing sync event extraction...')
try:
    payload = {
        'content': '苹果公司发布了新款iPhone。'
    }
    response = requests.post(f'{base_url}/event-extraction/', json=payload)
    print(f'Status: {response.status_code}')
    if response.status_code == 200:
        result = response.json()
        print('✅ Sync event extraction passed')
        print(f'Events found: {result[\"data\"][\"total\"]}')
    else:
        print('❌ Sync event extraction failed')
        print(f'Response: {response.text[:200]}')
except Exception as e:
    print(f'❌ Sync event extraction error: {e}')

# Test task submission (will fail without Redis, but we can check API structure)
print('\n3. Testing task submission API structure...')
try:
    payload = {
        'task_type': 'event_extraction',
        'content': '苹果公司发布了新款iPhone。',
        'priority': 5
    }
    response = requests.post(f'{base_url}/tasks/submit', json=payload)
    print(f'Status: {response.status_code}')
    if response.status_code == 500:
        print('⚠️  Task submission failed as expected (Redis not available)')
        print('✅ API structure is correct')
    elif response.status_code == 200:
        print('✅ Task submission passed')
    else:
        print(f'❌ Unexpected status: {response.status_code}')
        print(f'Response: {response.text[:200]}')
except Exception as e:
    print(f'❌ Task submission error: {e}')

print('\n=== API Structure Test Complete ===')
"
