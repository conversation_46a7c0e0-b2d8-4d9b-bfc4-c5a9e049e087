#!/usr/bin/env python3
"""
回调功能测试脚本

测试队列任务的回调功能是否正常工作。
"""

import requests
import json
import time
from datetime import datetime

# 测试配置
API_BASE_URL = "http://localhost:5001/api/v1"
CALLBACK_URL_TEMPLATE = "http://127.0.0.1/callback/{task_id}"
TEST_CONTENT = """
昨天下午，苹果公司在加利福尼亚州库比蒂诺的总部举行了新产品发布会。
CEO蒂姆·库克宣布推出全新的iPhone 15系列手机，该产品将于下个月正式上市销售。
"""


def test_callback_functionality():
    """测试回调功能"""
    print("🚀 开始回调功能测试")
    print(f"API地址: {API_BASE_URL}")
    print(f"回调URL模板: {CALLBACK_URL_TEMPLATE}")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)

    # 1. 提交带回调URL的任务
    print("1. 提交带回调URL的事件提取任务...")
    try:
        payload = {
            "task_type": "event_extraction",
            "content": TEST_CONTENT,
            "priority": 5,
            "callback_url": CALLBACK_URL_TEMPLATE
        }
        
        response = requests.post(
            f"{API_BASE_URL}/tasks/submit",
            json=payload,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            result = response.json()
            task_id = result['data']['task_id']
            print(f"✅ 任务提交成功")
            print(f"任务ID: {task_id}")
            print(f"回调URL将被设置为: http://127.0.0.1/callback/{task_id}")
            print(f"任务状态: {result['data']['status']}")
            return task_id
        else:
            print(f"❌ 任务提交失败，状态码: {response.status_code}")
            print(f"响应: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 任务提交异常: {str(e)}")
        return None


def monitor_task_progress(task_id):
    """监控任务进度"""
    print("=" * 60)
    print(f"2. 监控任务进度 (ID: {task_id})...")
    
    max_wait_time = 120  # 最大等待时间（秒）
    check_interval = 5   # 检查间隔（秒）
    waited_time = 0
    
    while waited_time < max_wait_time:
        try:
            response = requests.get(f"{API_BASE_URL}/tasks/status/{task_id}")
            if response.status_code == 200:
                result = response.json()
                data = result['data']
                status = data['status']
                
                print(f"⏱️  [{datetime.now().strftime('%H:%M:%S')}] 任务状态: {status}")
                
                if status in ['SUCCESS', 'FAILURE', 'REVOKED']:
                    print(f"✅ 任务完成，最终状态: {status}")
                    if status == 'SUCCESS':
                        print(f"完成时间: {data.get('completed_at', 'N/A')}")
                        print(f"处理时间: {data.get('processing_time', 'N/A')} 秒")
                    elif status == 'FAILURE':
                        print(f"错误信息: {data.get('error_message', 'N/A')}")
                    return status
                elif status in ['PENDING', 'STARTED']:
                    if status == 'STARTED':
                        print(f"   开始时间: {data.get('started_at', 'N/A')}")
                        print(f"   进度: {data.get('progress', 'N/A')}")
                    time.sleep(check_interval)
                    waited_time += check_interval
                else:
                    print(f"❌ 未知任务状态: {status}")
                    break
            else:
                print(f"❌ 状态查询失败，状态码: {response.status_code}")
                break
        except Exception as e:
            print(f"❌ 状态查询异常: {str(e)}")
            break
    
    if waited_time >= max_wait_time:
        print(f"⏰ 任务监控超时 ({max_wait_time} 秒)")
        return "TIMEOUT"
    
    return "UNKNOWN"


def get_task_result(task_id):
    """获取任务结果"""
    print("=" * 60)
    print(f"3. 获取任务结果 (ID: {task_id})...")
    
    try:
        response = requests.get(f"{API_BASE_URL}/tasks/result/{task_id}")
        if response.status_code == 200:
            result = response.json()
            data = result['data']
            print(f"✅ 任务结果获取成功")
            print(f"任务状态: {data['status']}")
            
            if data.get('result'):
                task_result = data['result']
                print(f"提取事件数量: {task_result.get('total', 0)}")
                print("提取的事件:")
                for i, event in enumerate(task_result.get('events', []), 1):
                    print(f"  {i}. 类型: {event['type']}")
                    print(f"     触发词: {event['trigger']}")
                    print(f"     描述: {event['description'][:50]}...")
            return True
        elif response.status_code == 202:
            print("⏳ 任务仍在执行中")
            return False
        else:
            print(f"❌ 任务结果获取失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 任务结果获取异常: {str(e)}")
        return False


def explain_callback_mechanism():
    """解释回调机制"""
    print("=" * 60)
    print("📋 回调机制说明:")
    print()
    print("1. **回调URL模板**: http://127.0.0.1/callback/{task_id}")
    print("   - {task_id} 会被自动替换为实际的任务ID")
    print("   - 例如: http://127.0.0.1/callback/12345678-1234-1234-1234-123456789abc")
    print()
    print("2. **回调时机**:")
    print("   - 任务成功完成时")
    print("   - 任务执行失败时")
    print()
    print("3. **回调方法**: POST")
    print()
    print("4. **回调数据格式**:")
    print("   ```json")
    print("   {")
    print('     "task_id": "任务ID",')
    print('     "task_type": "event_extraction",')
    print('     "status": "SUCCESS|FAILURE",')
    print('     "result": { ... },  // 成功时包含结果')
    print('     "error_message": "...",  // 失败时包含错误信息')
    print('     "completed_at": "2025-06-25T16:47:46.169230",')
    print('     "processing_time": 64.438113')
    print("   }")
    print("   ```")
    print()
    print("5. **注意事项**:")
    print("   - 回调URL必须是可访问的HTTP端点")
    print("   - 回调请求有超时和重试机制")
    print("   - 如果回调失败，会在日志中记录错误")


def main():
    """主测试流程"""
    # 解释回调机制
    explain_callback_mechanism()
    
    # 提交任务
    task_id = test_callback_functionality()
    if not task_id:
        print("❌ 无法继续测试，任务提交失败")
        return
    
    # 监控任务进度
    final_status = monitor_task_progress(task_id)
    
    # 获取任务结果
    if final_status == 'SUCCESS':
        get_task_result(task_id)
    
    print("=" * 60)
    print("🎉 回调功能测试完成")
    print()
    print("💡 重要提醒:")
    print(f"- 任务完成后，系统会向 http://127.0.0.1/callback/{task_id} 发送POST回调")
    print("- 请确保您的回调服务器正在运行并能接收此请求")
    print("- 可以查看Celery Worker日志来确认回调是否发送成功")


if __name__ == "__main__":
    main()
