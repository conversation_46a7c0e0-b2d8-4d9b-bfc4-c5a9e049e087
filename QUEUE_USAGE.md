# 队列功能使用指南

本文档介绍如何使用LLMFlowHub的异步队列功能。

## 概述

LLMFlowHub集成了基于Celery和Redis的异步任务队列系统，支持：
- 异步事件提取任务
- 任务状态查询
- 任务结果获取
- 任务取消
- 队列健康监控

## 环境要求

### 1. Redis服务
确保Redis服务正在运行：
```bash
# Windows (如果使用Redis for Windows)
redis-server

# Linux/Mac
redis-server /path/to/redis.conf
```

### 2. 环境变量配置
在`.env`文件中配置Redis连接信息：
```env
# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_RESULT_DB=1
REDIS_PASSWORD=

# 队列任务配置
MAX_RETRIES=3
RETRY_DELAY=60
TASK_TIMEOUT=3600
MAX_CONCURRENT_TASKS=1
RESULT_CACHE_TIME=86400
```

## 启动服务

### 1. 启动Web服务器
```bash
# 方式1：使用批处理脚本
run_web_server.bat

# 方式2：直接运行
python run.py
```

### 2. 启动Celery Worker
```bash
# 方式1：使用批处理脚本
run_celery_worker.bat

# 方式2：直接运行
celery -A app.tasks.celery_app worker --loglevel=info -c 1 -P solo
```

## API使用说明

### 1. 健康检查

#### 检查Redis连接
```http
GET /api/v1/tasks/health/redis
```

响应示例：
```json
{
  "status": "success",
  "msg": "Redis连接正常",
  "data": {
    "status": "success",
    "broker": {
      "status": "success",
      "message": "Broker Redis连接正常",
      "redis_version": "7.0.0",
      "used_memory_human": "1.2M",
      "connected_clients": 2,
      "db_size": 5
    },
    "result": {
      "status": "success",
      "message": "Result Redis连接正常",
      "redis_version": "7.0.0",
      "used_memory_human": "1.1M",
      "connected_clients": 1,
      "db_size": 0
    }
  }
}
```

#### 检查队列健康状态
```http
GET /api/v1/tasks/health/queue
```

### 2. 任务管理

#### 提交事件提取任务
```http
POST /api/v1/tasks/submit
Content-Type: application/json

{
  "task_type": "event_extraction",
  "content": "昨天下午，苹果公司发布了新款iPhone。",
  "priority": 5,
  "callback_url": "https://your-domain.com/callback"
}
```

响应示例：
```json
{
  "status": "success",
  "msg": "任务提交成功",
  "data": {
    "task_id": "12345678-1234-1234-1234-123456789abc",
    "task_type": "event_extraction",
    "status": "PENDING",
    "submitted_at": "2025-06-25T14:30:00",
    "estimated_completion_time": null
  }
}
```

#### 查询任务状态
```http
GET /api/v1/tasks/status/{task_id}
```

响应示例：
```json
{
  "status": "success",
  "msg": "查询成功",
  "data": {
    "task_id": "12345678-1234-1234-1234-123456789abc",
    "task_type": "event_extraction",
    "status": "SUCCESS",
    "progress": 100,
    "submitted_at": "2025-06-25T14:30:00",
    "started_at": "2025-06-25T14:30:05",
    "completed_at": "2025-06-25T14:30:15",
    "error_message": null,
    "retry_count": 0
  }
}
```

#### 获取任务结果
```http
GET /api/v1/tasks/result/{task_id}
```

响应示例：
```json
{
  "status": "success",
  "msg": "获取结果成功",
  "data": {
    "task_id": "12345678-1234-1234-1234-123456789abc",
    "task_type": "event_extraction",
    "status": "SUCCESS",
    "result": {
      "content": "昨天下午，苹果公司发布了新款iPhone。",
      "events": [
        {
          "type": "产品行为-发布",
          "trigger": "发布",
          "description": "苹果公司发布新款iPhone产品",
          "arguments": [
            {
              "role": "发布者",
              "values": ["苹果公司"]
            },
            {
              "role": "产品",
              "values": ["新款iPhone"]
            }
          ]
        }
      ],
      "time": "2025-06-25 14:30:15",
      "total": 1
    },
    "submitted_at": "2025-06-25T14:30:00",
    "started_at": "2025-06-25T14:30:05",
    "completed_at": "2025-06-25T14:30:15",
    "processing_time": 10.5,
    "error_message": null
  }
}
```

#### 取消任务
```http
POST /api/v1/tasks/cancel
Content-Type: application/json

{
  "task_id": "12345678-1234-1234-1234-123456789abc",
  "reason": "用户取消"
}
```

## 测试脚本

项目提供了完整的测试脚本`test_queue.py`，可以测试整个队列功能：

```bash
python test_queue.py
```

测试脚本会依次执行：
1. Redis连接测试
2. 队列健康检查
3. 任务提交
4. 任务状态轮询
5. 任务结果获取

## 任务状态说明

- `PENDING`: 任务已提交，等待执行
- `STARTED`: 任务已开始执行
- `SUCCESS`: 任务执行成功
- `FAILURE`: 任务执行失败
- `RETRY`: 任务重试中
- `REVOKED`: 任务已取消

## 注意事项

1. **Redis连接**: 确保Redis服务正常运行，并且配置正确
2. **Worker启动**: 必须启动Celery Worker才能处理任务
3. **任务超时**: 默认任务超时时间为1小时，可在配置中调整
4. **结果缓存**: 任务结果默认缓存24小时
5. **并发控制**: 默认每个Worker最多并发1个任务，可根据需要调整
6. **错误处理**: 任务失败会自动重试，最多重试3次

## 故障排除

### 1. Redis连接失败
- 检查Redis服务是否启动
- 检查Redis配置（主机、端口、密码）
- 检查防火墙设置

### 2. Worker无法启动
- 检查Python环境和依赖
- 检查Celery配置
- 查看错误日志

### 3. 任务执行失败
- 检查任务日志
- 检查LLM服务连接
- 检查输入数据格式

### 4. 任务结果获取失败
- 检查任务是否已完成
- 检查结果后端Redis连接
- 检查任务ID是否正确
