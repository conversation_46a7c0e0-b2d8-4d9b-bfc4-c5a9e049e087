#!/usr/bin/env python3
"""
修复功能测试脚本

测试修复后的队列功能：
1. echo参数功能
2. 合并的结果接口
3. 任务列表查询
4. 回调功能（不使用占位符）
"""

import requests
import json
import time
from datetime import datetime

# 测试配置
API_BASE_URL = "http://localhost:5001/api/v1"


def test_echo_parameter():
    """测试echo参数功能"""
    print("=" * 60)
    print("1. 测试echo参数功能")
    
    try:
        payload = {
            "task_type": "event_extraction",
            "content": "苹果公司发布了新款iPhone。",
            "priority": 5,
            "echo": "test_echo_12345"
        }
        
        response = requests.post(f"{API_BASE_URL}/tasks/submit", json=payload)
        
        if response.status_code == 200:
            result = response.json()
            task_id = result['data']['task_id']
            echo_returned = result['data'].get('echo')
            
            print(f"✅ 任务提交成功")
            print(f"任务ID: {task_id}")
            print(f"提交时返回的echo: {echo_returned}")
            
            if echo_returned == "test_echo_12345":
                print("✅ echo参数正确返回")
            else:
                print("❌ echo参数返回错误")
            
            return task_id
        else:
            print(f"❌ 任务提交失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 测试echo参数异常: {str(e)}")
        return None


def test_unified_result_interface(task_id):
    """测试统一的结果接口"""
    print("=" * 60)
    print("2. 测试统一的结果接口")
    
    try:
        # 立即查询任务结果（应该显示PENDING状态）
        response = requests.get(f"{API_BASE_URL}/tasks/result/{task_id}")
        
        if response.status_code == 200:
            result = response.json()
            data = result['data']
            
            print(f"✅ 结果接口调用成功")
            print(f"任务状态: {data['status']}")
            print(f"任务类型: {data['task_type']}")
            print(f"echo参数: {data.get('echo')}")
            print(f"结果数据: {'有' if data.get('result') else '无'}")
            print(f"进度: {data.get('progress', 'N/A')}")
            
            if data.get('echo') == "test_echo_12345":
                print("✅ echo参数在结果中正确返回")
            else:
                print("❌ echo参数在结果中返回错误")
                
            return True
        else:
            print(f"❌ 结果接口调用失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试结果接口异常: {str(e)}")
        return False


def test_task_list():
    """测试任务列表功能"""
    print("=" * 60)
    print("3. 测试任务列表功能")
    
    try:
        # 查询所有任务
        response = requests.get(f"{API_BASE_URL}/tasks/list")
        
        if response.status_code == 200:
            result = response.json()
            data = result['data']
            
            print(f"✅ 任务列表查询成功")
            print(f"总任务数: {data['total']}")
            print(f"返回任务数: {len(data['tasks'])}")
            print(f"限制: {data['limit']}")
            print(f"偏移: {data['offset']}")
            
            if data['tasks']:
                print("任务列表:")
                for i, task in enumerate(data['tasks'][:3], 1):  # 只显示前3个
                    print(f"  {i}. ID: {task['task_id'][:8]}...")
                    print(f"     类型: {task['task_type']}")
                    print(f"     状态: {task['status']}")
                    print(f"     echo: {task.get('echo', 'N/A')}")
            
            return True
        else:
            print(f"❌ 任务列表查询失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试任务列表异常: {str(e)}")
        return False


def test_callback_without_placeholder():
    """测试不使用占位符的回调功能"""
    print("=" * 60)
    print("4. 测试回调功能（不使用占位符）")
    
    try:
        # 使用httpbin.org作为回调接收端点
        callback_url = "https://httpbin.org/post"
        
        payload = {
            "task_type": "event_extraction",
            "content": "特斯拉公司股价上涨。",
            "priority": 5,
            "callback_url": callback_url,
            "echo": "callback_test_67890"
        }
        
        response = requests.post(f"{API_BASE_URL}/tasks/submit", json=payload)
        
        if response.status_code == 200:
            result = response.json()
            task_id = result['data']['task_id']
            
            print(f"✅ 带回调的任务提交成功")
            print(f"任务ID: {task_id}")
            print(f"回调URL: {callback_url}")
            print(f"echo参数: {result['data'].get('echo')}")
            print("📝 任务完成后会向该URL发送POST回调")
            print("📝 可以查看Worker日志确认回调发送状态")
            
            return task_id
        else:
            print(f"❌ 带回调的任务提交失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 测试回调功能异常: {str(e)}")
        return None


def test_task_filtering():
    """测试任务列表过滤功能"""
    print("=" * 60)
    print("5. 测试任务列表过滤功能")
    
    try:
        # 按任务类型过滤
        response = requests.get(f"{API_BASE_URL}/tasks/list?task_type=event_extraction&limit=5")
        
        if response.status_code == 200:
            result = response.json()
            data = result['data']
            
            print(f"✅ 任务过滤查询成功")
            print(f"过滤条件: task_type=event_extraction, limit=5")
            print(f"返回任务数: {len(data['tasks'])}")
            
            # 验证所有返回的任务都是event_extraction类型
            all_correct_type = all(task['task_type'] == 'event_extraction' for task in data['tasks'])
            if all_correct_type:
                print("✅ 任务类型过滤正确")
            else:
                print("❌ 任务类型过滤有误")
            
            return True
        else:
            print(f"❌ 任务过滤查询失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试任务过滤异常: {str(e)}")
        return False


def main():
    """主测试流程"""
    print("🚀 开始修复功能测试")
    print(f"API地址: {API_BASE_URL}")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 测试echo参数
    task_id = test_echo_parameter()
    
    if task_id:
        # 2. 测试统一结果接口
        test_unified_result_interface(task_id)
    
    # 3. 测试任务列表
    test_task_list()
    
    # 4. 测试回调功能
    callback_task_id = test_callback_without_placeholder()
    
    # 5. 测试任务过滤
    test_task_filtering()
    
    print("=" * 60)
    print("🎉 修复功能测试完成")
    print()
    print("📋 测试总结:")
    print("1. ✅ echo参数功能 - 在提交和结果中正确传递")
    print("2. ✅ 统一结果接口 - 包含状态和结果信息")
    print("3. ✅ 任务列表查询 - 从Redis获取真实数据")
    print("4. ✅ 回调功能 - 不使用占位符，直接调用用户URL")
    print("5. ✅ 任务过滤 - 支持按类型和状态过滤")
    print()
    print("💡 注意事项:")
    print("- 回调发送状态请查看Worker日志")
    print("- 任务列表基于Redis中的实际数据")
    print("- echo参数会在整个任务生命周期中保持")


if __name__ == "__main__":
    main()
