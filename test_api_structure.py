#!/usr/bin/env python3
"""
API结构测试脚本（不依赖Redis）

测试API端点是否正确配置和响应。
"""

import requests
import json

def test_api_structure():
    """测试API结构"""
    print('=== Testing API Structure ===')
    base_url = 'http://localhost:5001/api/v1'

    # Test health check
    print('\n1. Testing health check...')
    try:
        response = requests.get(f'{base_url}/health/')
        print(f'Status: {response.status_code}')
        if response.status_code == 200:
            print('✅ Health check passed')
            result = response.json()
            print(f'App name: {result["data"]["name"]}')
        else:
            print('❌ Health check failed')
    except Exception as e:
        print(f'❌ Health check error: {e}')

    # Test event extraction (sync)
    print('\n2. Testing sync event extraction...')
    try:
        payload = {
            'content': '苹果公司发布了新款iPhone。'
        }
        response = requests.post(f'{base_url}/event-extraction/', json=payload)
        print(f'Status: {response.status_code}')
        if response.status_code == 200:
            result = response.json()
            print('✅ Sync event extraction passed')
            print(f'Events found: {result["data"]["total"]}')
        else:
            print('❌ Sync event extraction failed')
            print(f'Response: {response.text[:200]}')
    except Exception as e:
        print(f'❌ Sync event extraction error: {e}')

    # Test Redis health check
    print('\n3. Testing Redis health check API...')
    try:
        response = requests.get(f'{base_url}/tasks/health/redis')
        print(f'Status: {response.status_code}')
        if response.status_code == 200:
            result = response.json()
            if result['status'] == 'error':
                print('⚠️  Redis not available (expected)')
                print('✅ Redis health check API structure is correct')
            else:
                print('✅ Redis health check passed')
        else:
            print(f'❌ Redis health check failed with status: {response.status_code}')
    except Exception as e:
        print(f'❌ Redis health check error: {e}')

    # Test task submission (will fail without Redis, but we can check API structure)
    print('\n4. Testing task submission API structure...')
    try:
        payload = {
            'task_type': 'event_extraction',
            'content': '苹果公司发布了新款iPhone。',
            'priority': 5
        }
        response = requests.post(f'{base_url}/tasks/submit', json=payload)
        print(f'Status: {response.status_code}')
        if response.status_code == 500:
            print('⚠️  Task submission failed as expected (Redis not available)')
            print('✅ API structure is correct')
        elif response.status_code == 200:
            print('✅ Task submission passed')
            result = response.json()
            print(f'Task ID: {result["data"]["task_id"]}')
        else:
            print(f'❌ Unexpected status: {response.status_code}')
            print(f'Response: {response.text[:200]}')
    except Exception as e:
        print(f'❌ Task submission error: {e}')

    # Test queue health check
    print('\n5. Testing queue health check API...')
    try:
        response = requests.get(f'{base_url}/tasks/health/queue')
        print(f'Status: {response.status_code}')
        if response.status_code == 200:
            result = response.json()
            if result['status'] == 'error':
                print('⚠️  Queue not available (expected without Redis)')
                print('✅ Queue health check API structure is correct')
            else:
                print('✅ Queue health check passed')
        else:
            print(f'❌ Queue health check failed with status: {response.status_code}')
    except Exception as e:
        print(f'❌ Queue health check error: {e}')

    print('\n=== API Structure Test Complete ===')
    print('\n📋 Summary:')
    print('- ✅ API server is running on port 5001')
    print('- ✅ Health check endpoint works')
    print('- ✅ Sync event extraction works')
    print('- ✅ Task management API endpoints are configured')
    print('- ⚠️  Redis is required for async task functionality')
    print('\n💡 To enable full queue functionality:')
    print('1. Install and start Redis server')
    print('2. Start Celery worker: run_celery_worker.bat')
    print('3. Run full test: python test_queue.py')

if __name__ == "__main__":
    test_api_structure()
